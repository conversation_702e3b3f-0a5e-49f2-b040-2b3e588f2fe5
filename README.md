# Vibe Typer

A cross-platform desktop application that provides real-time speech-to-text transcription with AI-powered features. Now with user authentication and cloud-based processing!

## Features

- **Real-time Speech Recognition**: Uses OpenAI's Whisper API for accurate transcription
- **AI-Powered Commands**: Intelligent text processing with GPT models (Write, Answer, Rewrite, Reply modes)
- **User Authentication**: Secure sign-in with Supabase authentication - no API keys required!
- **Cloud Processing**: Backend service handles all AI processing securely
- **Global Hotkeys**: Press `Ctrl+Shift+Space` (or `Cmd+Shift+Space` on macOS) to start/stop recording
- **Universal Text Input**: Automatically types transcribed text into any active application
- **System Tray Integration**: Runs in the background with system tray icon
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Usage Tracking**: Monitor your API usage and costs
- **Configurable**: Customizable hotkeys, language settings, and audio options

## Installation

### Prerequisites

1. **Node.js** (v18 or higher)
2. **Audio Recording Dependencies**:
   - **Linux**: Install SoX: `sudo apt-get install sox` (Ubuntu/Debian) or `sudo dnf install sox` (Fedora)
   - **macOS**: SoX is included or install via Homebrew: `brew install sox`
   - **Windows**: SoX will be automatically handled
3. **Supabase Account**: For user authentication
4. **OpenAI API Key**: For the backend service

### Quick Setup

Use the provided setup scripts for easy installation:

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd vibe-typer
   ```

2. **Set up the backend:**
   ```bash
   ./setup-backend.sh
   ```

3. **Set up the client:**
   ```bash
   ./setup-client.sh
   ```

4. **Configure the backend:**
   - Edit `backend/.env` with your configuration
   - Set up your Supabase database using `backend/supabase-schema.sql`

5. **Start the services:**
   ```bash
   # Terminal 1: Start backend
   cd backend && npm run dev

   # Terminal 2: Start client
   npm run dev
   ```

### Manual Setup

If you prefer manual setup:

1. **Backend Setup:**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   # Edit .env with your configuration
   npm run build
   npm run dev
   ```

2. **Client Setup:**
   ```bash
   npm install
   npm run build
   npm run dev
   ```

## Configuration

### Backend Configuration

Edit `backend/.env` with your settings:

```env
# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
```

### Database Setup

1. Create a Supabase project at [supabase.com](https://supabase.com)
2. Run the SQL in `backend/supabase-schema.sql` in your Supabase SQL editor
3. Copy your project URL and keys to the `.env` file

### User Authentication

1. Open the application
2. Click "Settings" to access the authentication panel
3. Create a new account or sign in with existing credentials
4. Start using speech-to-text immediately!

**Note**: No API keys required for users! The backend handles all OpenAI API calls securely.

### Audio Settings

The application uses the following default audio settings:
- Sample Rate: 16kHz
- Channels: Mono
- Format: 16-bit PCM

### Hotkey Configuration

Default hotkey: `Ctrl+Shift+Space` (Windows/Linux) or `Cmd+Shift+Space` (macOS)

You can modify the hotkey in the configuration section of the application.

## Usage

### Basic Speech-to-Text

1. Press and hold the global hotkey (`Ctrl+Shift+Space`)
2. Speak clearly into your microphone
3. Release the hotkey to stop recording
4. The transcribed text will be automatically typed into the active application

### AI-Powered Features

You can use voice commands to trigger AI-powered text processing:

- **"Write [your request]"**: Generate text based on your request
- **"Answer [your question]"**: Get answers to questions
- **"Rewrite [instructions]"**: Rewrite existing text
- **"Reply [instructions]"**: Generate replies to messages

## System Integration

### Text Insertion

The application uses different methods for text insertion depending on the platform:

- **Windows**: PowerShell SendKeys
- **macOS**: osascript (AppleScript)
- **Linux**: xdotool (requires installation: `sudo apt-get install xdotool`)

### Permissions

Some platforms may require additional permissions:

- **macOS**: Accessibility permissions for keyboard simulation
- **Linux**: X11 access for global hotkeys and text insertion
- **Windows**: No special permissions required

## Development

### Project Structure

```
src/
├── main.ts              # Main Electron process
├── audio/
│   └── recorder.ts      # Audio recording functionality
├── speech/
│   └── recognizer.ts    # Speech recognition and AI processing
├── text/
│   └── inserter.ts      # Cross-platform text insertion
└── config/
    └── manager.ts       # Configuration management

assets/
├── index.html           # Main UI
├── renderer.js          # Renderer process logic
└── *.png               # Application icons
```

### Available Scripts

- `npm run build`: Compile TypeScript
- `npm run dev`: Build and run in development mode
- `npm run start`: Run the built application
- `npm run pack`: Package for distribution
- `npm run dist`: Create distributable packages

### Building for Distribution

```bash
# Build for current platform
npm run dist

# The built packages will be in the 'release' directory
```

## Troubleshooting

### Audio Recording Issues

1. **Linux**: Ensure SoX is installed and microphone permissions are granted
2. **macOS**: Grant microphone permissions in System Preferences
3. **Windows**: Check microphone privacy settings

### Text Insertion Not Working

1. **Linux**: Install xdotool: `sudo apt-get install xdotool`
2. **macOS**: Grant Accessibility permissions in System Preferences
3. **All platforms**: Try running the application as administrator/root (not recommended for regular use)

### Global Hotkey Conflicts

If the default hotkey conflicts with other applications:
1. Open the application settings
2. Choose a different hotkey combination
3. Save the configuration and restart the application

## API Usage and Costs

This application uses OpenAI's APIs:
- **Whisper API**: ~$0.006 per minute of audio
- **GPT-4 API**: Variable pricing based on tokens used

Monitor your usage at [OpenAI Usage Dashboard](https://platform.openai.com/usage).

## Privacy and Security

- Audio data is sent to OpenAI for transcription (when using the API)
- No audio data is stored locally after transcription
- API keys are stored locally in the application's configuration file
- For maximum privacy, consider using a local Whisper model (future feature)

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on your target platform(s)
5. Submit a pull request

## Roadmap

- [ ] Local Whisper model support (offline mode)
- [ ] Custom vocabulary and language models
- [ ] Voice activity detection
- [ ] Multiple microphone support
- [ ] Noise reduction and audio preprocessing
- [ ] Plugin system for custom text processing
- [ ] Cloud synchronization of settings
- [ ] Advanced hotkey combinations
- [ ] Real-time transcription display
- [ ] Audio playback for verification
