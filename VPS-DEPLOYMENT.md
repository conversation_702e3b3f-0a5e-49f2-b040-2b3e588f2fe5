# Deploy Vibe Typer Backend to Your VPS

Complete step-by-step guide to deploy the backend service to your own Virtual Private Server.

## Prerequisites

### What You Need
- A VPS with Ubuntu 20.04+ (or similar Linux distribution)
- Root or sudo access to the VPS
- A domain name (optional but recommended)
- OpenAI API key
- Supabase account and project

### Recommended VPS Specs
- **Minimum**: 1 CPU, 1GB RAM, 20GB storage
- **Recommended**: 2 CPU, 2GB RAM, 40GB storage
- **Providers**: DigitalOcean, Linode, Vultr, AWS EC2, etc.

## Step 1: Initial Server Setup

### Connect to Your VPS
```bash
ssh root@your-server-ip
# or
ssh your-username@your-server-ip
```

### Update the System
```bash
sudo apt update && sudo apt upgrade -y
```

### Create a Non-Root User (if using root)
```bash
# Create new user
adduser vibetyper

# Add to sudo group
usermod -aG sudo vibetyper

# Switch to new user
su - vibetyper
```

### Install Essential Packages
```bash
sudo apt install -y curl wget git unzip software-properties-common
```

## Step 2: Install Node.js

### Install Node.js 18.x
```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# Install Node.js
sudo apt install -y nodejs

# Verify installation
node --version  # Should show v18.x.x
npm --version   # Should show 9.x.x or higher
```

### Install PM2 (Process Manager)
```bash
sudo npm install -g pm2
```

## Step 3: Set Up the Application

### Create Application Directory
```bash
sudo mkdir -p /var/www/vibe-typer
sudo chown vibetyper:vibetyper /var/www/vibe-typer
cd /var/www/vibe-typer
```

### Upload Your Backend Code

**Option A: Using Git (if you have a repository)**
```bash
git clone https://github.com/your-username/vibe-typer.git .
cd backend
```

**Option B: Upload Files Manually**
```bash
# From your local machine, upload the backend folder
scp -r backend/ vibetyper@your-server-ip:/var/www/vibe-typer/

# Then on the server
cd /var/www/vibe-typer/backend
```

### Install Dependencies
```bash
npm install --production
```

### Build the Application
```bash
npm run build
```

## Step 4: Configure Environment Variables

### Create Production Environment File
```bash
cp .env.example .env
nano .env
```

### Edit the .env File
```env
# Server Configuration
NODE_ENV=production
PORT=3001

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# JWT Configuration
JWT_SECRET=your-very-secure-jwt-secret-at-least-32-characters-long

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
```

**Important**: Make sure to:
- Use a strong JWT_SECRET (32+ random characters)
- Replace all placeholder values with your actual credentials
- Keep this file secure (never commit to git)

## Step 5: Set Up PM2 Process Management

### Create PM2 Ecosystem File
```bash
nano ecosystem.config.js
```

### Add PM2 Configuration
```javascript
module.exports = {
  apps: [{
    name: 'vibe-typer-backend',
    script: 'dist/server.js',
    cwd: '/var/www/vibe-typer/backend',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: '/var/log/vibe-typer/error.log',
    out_file: '/var/log/vibe-typer/out.log',
    log_file: '/var/log/vibe-typer/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

### Create Log Directory
```bash
sudo mkdir -p /var/log/vibe-typer
sudo chown vibetyper:vibetyper /var/log/vibe-typer
```

### Start the Application
```bash
pm2 start ecosystem.config.js
```

### Set Up PM2 Auto-Start
```bash
# Save current PM2 processes
pm2 save

# Generate startup script
pm2 startup

# Follow the instructions shown (usually run a command with sudo)
```

### Verify Application is Running
```bash
pm2 status
pm2 logs vibe-typer-backend
```

## Step 6: Install and Configure Nginx

### Install Nginx
```bash
sudo apt install -y nginx
```

### Create Nginx Configuration
```bash
sudo nano /etc/nginx/sites-available/vibe-typer
```

### Add Nginx Configuration

**For IP-based access:**
```nginx
server {
    listen 80;
    server_name your-server-ip;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # CORS headers for Electron app
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Authorization, Content-Type";
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Authorization, Content-Type";
            return 204;
        }
    }
}
```

**For domain-based access:**
```nginx
server {
    listen 80;
    server_name api.yourdomain.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # CORS headers for Electron app
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Authorization, Content-Type";
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Authorization, Content-Type";
            return 204;
        }
    }
}
```

### Enable the Site
```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/vibe-typer /etc/nginx/sites-enabled/

# Remove default site
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## Step 7: Set Up SSL with Let's Encrypt (Optional but Recommended)

### Install Certbot
```bash
sudo apt install -y certbot python3-certbot-nginx
```

### Get SSL Certificate
```bash
# For domain-based setup
sudo certbot --nginx -d api.yourdomain.com

# Follow the prompts to set up SSL
```

### Set Up Auto-Renewal
```bash
# Test renewal
sudo certbot renew --dry-run

# Auto-renewal is usually set up automatically
```

## Step 8: Configure Firewall

### Set Up UFW Firewall
```bash
# Enable UFW
sudo ufw enable

# Allow SSH
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 'Nginx Full'

# Check status
sudo ufw status
```

## Step 9: Test the Deployment

### Test Backend Health
```bash
# Test locally on server
curl http://localhost:3001/api/health

# Test through Nginx
curl http://your-server-ip/api/health
# or
curl http://api.yourdomain.com/api/health
```

### Expected Response
```json
{
  "success": true,
  "message": "Vibe Typer Backend is healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "version": "1.0.0",
  "environment": "production"
}
```

## Step 10: Update Client Configuration

### Update Client App
Edit `src/config/manager.ts` in your client app:

```typescript
private defaultConfig: AppConfig = {
  authConfig: {
    supabaseUrl: 'https://your-project.supabase.co',
    supabaseAnonKey: 'your-supabase-anon-key',
    backendUrl: 'http://your-server-ip', // or https://api.yourdomain.com
  },
  // ... rest of config
};
```

### Rebuild and Distribute Client
```bash
# On your development machine
npm run build
npm run dist
```

## Monitoring and Maintenance

### Check Application Status
```bash
# PM2 status
pm2 status
pm2 logs vibe-typer-backend

# System resources
htop
df -h

# Nginx status
sudo systemctl status nginx
```

### View Logs
```bash
# Application logs
pm2 logs vibe-typer-backend

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# System logs
sudo journalctl -u nginx -f
```

### Update Application
```bash
# Stop application
pm2 stop vibe-typer-backend

# Update code (if using git)
git pull origin main

# Install dependencies and rebuild
npm install --production
npm run build

# Restart application
pm2 restart vibe-typer-backend
```

## Troubleshooting

### Common Issues

**Application won't start:**
```bash
# Check logs
pm2 logs vibe-typer-backend

# Check environment variables
cat .env

# Test manually
cd /var/www/vibe-typer/backend
node dist/server.js
```

**Can't connect from client:**
```bash
# Check if app is running
curl http://localhost:3001/api/health

# Check Nginx
sudo nginx -t
sudo systemctl status nginx

# Check firewall
sudo ufw status
```

**SSL issues:**
```bash
# Check certificate
sudo certbot certificates

# Renew if needed
sudo certbot renew
```

## Security Best Practices

### Regular Updates
```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Node.js dependencies
npm audit fix
```

### Backup Strategy
```bash
# Backup application
tar -czf vibe-typer-backup-$(date +%Y%m%d).tar.gz /var/www/vibe-typer

# Backup environment file
cp .env .env.backup
```

### Monitor Resources
```bash
# Set up monitoring
pm2 install pm2-server-monit

# Check disk space regularly
df -h

# Monitor logs for errors
tail -f /var/log/vibe-typer/error.log
```

Your backend should now be running on your VPS and accessible to client applications!
