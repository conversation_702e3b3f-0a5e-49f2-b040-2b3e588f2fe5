import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { pcm16leToWav } from './pcmToWav';
import { ipcMain, BrowserWindow } from 'electron';

// Web Audio API based recording - no external dependencies required

export class AudioRecorder extends EventEmitter {
  private isRecording = false;
  private audioChunks: Buffer[] = [];
  private tempFilePath: string;
  private recordingOptions: any;
  private recordingStartTime: number = 0;
  private minRecordingDuration: number = 100; // Minimum 100ms recording
  private silenceThreshold: number = 0.01; // Audio level threshold for silence detection
  private minSpeechDuration: number = 500; // Minimum 500ms of actual speech required
  private audioLevels: number[] = []; // Store audio levels for analysis
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    super();
    this.tempFilePath = path.join(os.tmpdir(), `whisper-recording-${Date.now()}.wav`);
    this.recordingOptions = {
      sampleRateHertz: 16000,
      channelCount: 1,
      sampleRate: 16000
    };

    // Set up IPC handlers for Web Audio API communication
    this.setupIpcHandlers();
  }

  private setupIpcHandlers(): void {
    // Handle audio data from renderer process
    ipcMain.on('audio-data', (event, audioData: number[]) => {
      if (this.isRecording) {
        // Convert Float32Array to 16-bit PCM Buffer
        const buffer = this.float32ArrayToInt16Buffer(audioData);
        this.audioChunks.push(buffer);
      }
    });

    // Handle recording errors from renderer
    ipcMain.on('audio-error', (event, error: string) => {
      console.error('Audio recording error from renderer:', error);
      this.isRecording = false;
      this.emit('error', new Error(error));
    });
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  async start(): Promise<void> {
    if (this.isRecording) {
      // Recording is already in progress, ignore silently
      return;
    }

    try {
      this.isRecording = true;
      this.audioChunks = [];
      this.recordingStartTime = Date.now();

      console.log('Starting audio recording...');

      if (!this.mainWindow) {
        throw new Error('Main window not set. Call setMainWindow() first.');
      }

      // Send message to renderer process to start Web Audio API recording
      this.mainWindow.webContents.send('start-audio-recording', {
        sampleRate: this.recordingOptions.sampleRate,
        channelCount: this.recordingOptions.channelCount
      });

      console.log('Audio recording started');
      this.emit('recording-started');

    } catch (error) {
      this.isRecording = false;
      const friendlyError = this.createUserFriendlyError(error);
      console.error('Failed to start recording:', friendlyError.message);
      throw friendlyError;
    }
  }

  async stop(outputFormat: 'wav' | 'pcm' = 'wav'): Promise<Buffer | null> {
    if (!this.isRecording) {
      // No recording in progress, ignore silently
      return null;
    }

    try {
      const recordingDuration = Date.now() - this.recordingStartTime;
      console.log(`Stopping audio recording after ${recordingDuration}ms...`);

      this.isRecording = false;

      // Stop the recording stream
      if (this.mainWindow) {
        this.mainWindow.webContents.send('stop-audio-recording');
      }

      // Wait a brief moment for any remaining data to be captured
      await new Promise(resolve => setTimeout(resolve, 50));

      // Combine all audio chunks into a single buffer
      const audioBuffer = this.audioChunks.length > 0 ? Buffer.concat(this.audioChunks) : null;

      // Clear the chunks array
      this.audioChunks = [];

      // Check if recording was too short
      if (recordingDuration < this.minRecordingDuration) {
        console.warn(`Recording too short (${recordingDuration}ms), minimum is ${this.minRecordingDuration}ms`);
        this.emit('recording-too-short', { duration: recordingDuration, minimum: this.minRecordingDuration });
        return null;
      }

      // Check if we actually captured any audio data
      if (!audioBuffer || audioBuffer.length === 0) {
        console.warn('No audio data captured during recording');
        this.emit('no-audio-captured');
        return null;
      }

      // Analyze audio for silence detection
      const audioAnalysis = this.analyzeAudioBuffer(audioBuffer);
      console.log(`Audio analysis: avg=${audioAnalysis.averageLevel.toFixed(4)}, max=${audioAnalysis.maxLevel.toFixed(4)}, speechDuration=${audioAnalysis.speechDuration}ms`);
      
      // Check if the audio is mostly silence
      if (audioAnalysis.averageLevel < this.silenceThreshold) {
        console.warn(`Recording is mostly silence (avg level: ${audioAnalysis.averageLevel.toFixed(4)} < threshold: ${this.silenceThreshold})`);
        this.emit('recording-is-silence', { 
          averageLevel: audioAnalysis.averageLevel, 
          threshold: this.silenceThreshold,
          speechDuration: audioAnalysis.speechDuration
        });
        return null;
      }

      // Check if there's enough actual speech content
      if (audioAnalysis.speechDuration < this.minSpeechDuration) {
        console.warn(`Insufficient speech detected (${audioAnalysis.speechDuration}ms < ${this.minSpeechDuration}ms required)`);
        this.emit('insufficient-speech', { 
          speechDuration: audioAnalysis.speechDuration, 
          required: this.minSpeechDuration 
        });
        return null;
      }

      console.log(`Audio recording stopped. Captured ${audioBuffer.length} bytes in ${recordingDuration}ms`);
      this.emit('recording-stopped', { duration: recordingDuration, bytes: audioBuffer.length });

      // Convert to WAV format if requested (default behavior)
      if (outputFormat === 'wav') {
        const wavBuffer = pcm16leToWav(audioBuffer, this.recordingOptions.sampleRateHertz, 1);
        
        // Debug: Write WAV buffer to temp file if DEBUG_AUDIO is enabled
        if (process.env.DEBUG_AUDIO === '1') {
          const debugFilePath = this.tempFilePath.replace(/\.[^.]*$/, '.wav');
          try {
            fs.writeFileSync(debugFilePath, wavBuffer);
            console.log(`Debug: WAV buffer written to ${debugFilePath} for manual playback`);
          } catch (error) {
            console.warn('Debug: Failed to write WAV buffer to temp file:', error);
          }
        }
        
        return wavBuffer;
      }

      // Return raw PCM data if specified
      return audioBuffer;

    } catch (error) {
      this.isRecording = false;
      const friendlyError = this.createUserFriendlyError(error);
      console.error('Failed to stop recording:', friendlyError.message);
      throw friendlyError;
    }
  }

  isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  async cleanup(): Promise<void> {
    if (this.isRecording) {
      await this.stop();
    }
    
    // Clean up temporary files
    try {
      if (fs.existsSync(this.tempFilePath)) {
        fs.unlinkSync(this.tempFilePath);
      }
    } catch (error) {
      console.warn('Failed to clean up temporary audio file:', error);
    }
  }

  // Method to get supported audio formats
  getSupportedFormats(): string[] {
    return ['wav', 'pcm']; // naudiodon provides raw PCM, we convert to WAV
  }

  // Method to get available audio devices
  getAvailableAudioDevices(): any[] {
    return this.getAvailableDevices().filter(device => device.maxInputChannels > 0);
  }
    return ['wav', 'mp3', 'flac'];
  }

  // Method to set audio quality settings
  setAudioSettings(settings: {
    sampleRate?: number;
    channels?: number;
    bitDepth?: number;
  }): void {
    if (settings.sampleRate) {
      this.recordingOptions.sampleRateHertz = settings.sampleRate;
    }
    console.log('Audio settings updated:', settings);
  }

  /**
   * Convert Float32Array audio data to 16-bit PCM Buffer
   */
  private float32ArrayToInt16Buffer(float32Array: number[]): Buffer {
    const buffer = Buffer.alloc(float32Array.length * 2);
    for (let i = 0; i < float32Array.length; i++) {
      // Convert from [-1, 1] to [-32768, 32767]
      const sample = Math.max(-1, Math.min(1, float32Array[i]));
      const int16Sample = Math.round(sample * 32767);
      buffer.writeInt16LE(int16Sample, i * 2);
    }
    return buffer;
  }

  /**
   * Get available audio devices (placeholder for Web Audio API)
   */
  private getAvailableDevices(): any[] {
    // Web Audio API doesn't provide device enumeration in the same way
    // This would need to be implemented in the renderer process
    return [];
  }

  /**
   * Create user-friendly error messages
   */
  private createUserFriendlyError(error: any): Error {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Web Audio API specific error handling
    if (errorMessage.includes('NotFoundError') || errorMessage.includes('No audio devices found')) {
      return new Error('Audio recording failed: No microphone found. Please connect a microphone and try again.');
    }

    if (errorMessage.includes('NotAllowedError') || errorMessage.includes('Permission denied')) {
      return new Error('Audio recording failed: Permission denied. Please grant microphone permissions to this application.');
    }

    if (errorMessage.includes('NotReadableError') || errorMessage.includes('Device or resource busy')) {
      return new Error('Audio recording failed: Microphone is being used by another application. Please close other audio applications and try again.');
    }

    if (errorMessage.includes('AbortError')) {
      return new Error('Audio recording failed: Recording was aborted. Please try again.');
    }

    if (errorMessage.includes('NotSupportedError')) {
      return new Error('Audio recording failed: Audio recording is not supported in this environment.');
    }

    return new Error(`Audio recording failed: ${errorMessage}`);
  }

  /**
   * Stop recording and return audio data in WAV format
   * @deprecated Use stop('wav') instead - this method is kept for backward compatibility
   */
  async stopAndGetWAV(): Promise<Buffer | null> {
    return this.stop('wav');
  }

  /**
   * Force stop recording (for cleanup)
   */
  forceStop(): void {
    if (this.isRecording) {
      this.isRecording = false;
      if (this.audioInput) {
        try {
          this.audioInput.quit();
        } catch (error) {
          console.warn('Error force stopping audio stream:', error);
        }
        this.audioInput = null;
      }
      this.audioChunks = [];
      console.log('Audio recording force stopped');
    }
  }

  /**
   * Analyze audio buffer for silence detection
   */
  private analyzeAudioBuffer(audioBuffer: Buffer): {
    averageLevel: number;
    maxLevel: number;
    speechDuration: number;
  } {
    let sum = 0;
    let maxLevel = 0;
    const samples: number[] = [];
    for (let i = 0; i < audioBuffer.length; i += 2) {
      const sample = audioBuffer.readInt16LE(i);
      const norm = Math.abs(sample) / 32768;
      samples.push(norm);
      sum += norm;
      maxLevel = Math.max(maxLevel, norm);
    }

    if (samples.length === 0) {
      return { averageLevel: 0, maxLevel: 0, speechDuration: 0 };
    }

    const averageLevel = sum / samples.length;

    // Calculate speech duration (time above silence threshold)
    const sampleRate = this.recordingOptions.sampleRateHertz;
    const samplesPerMs = sampleRate / 1000;
    let speechSamples = 0;

    // Use a sliding window to detect speech segments
    const windowSize = Math.floor(samplesPerMs * 100); // 100ms window
    for (let i = 0; i < samples.length - windowSize; i += windowSize) {
      const windowSamples = samples.slice(i, i + windowSize);
      const windowAverage = windowSamples.reduce((sum, sample) => sum + sample, 0) / windowSamples.length;
      
      if (windowAverage > this.silenceThreshold) {
        speechSamples += windowSize;
      }
    }

    const speechDuration = speechSamples / samplesPerMs;

    return {
      averageLevel,
      maxLevel,
      speechDuration
    };
  }

  /**
   * Configure silence detection parameters
   */
  setSilenceDetectionSettings(settings: {
    silenceThreshold?: number;
    minSpeechDuration?: number;
  }): void {
    if (settings.silenceThreshold !== undefined) {
      this.silenceThreshold = settings.silenceThreshold;
    }
    if (settings.minSpeechDuration !== undefined) {
      this.minSpeechDuration = settings.minSpeechDuration;
    }
    console.log('Silence detection settings updated:', {
      silenceThreshold: this.silenceThreshold,
      minSpeechDuration: this.minSpeechDuration
    });
  }
}

// Audio recording implementation notes:
//
// This implementation uses naudiodon (PortAudio wrapper) which provides:
//
// 1. Cross-platform audio recording:
//    - Windows: WASAPI, DirectSound, MME
//    - macOS: CoreAudio
//    - Linux: ALSA, JACK, PulseAudio
//
// 2. Zero external dependencies:
//    - PortAudio is statically linked
//    - No need for users to install SoX or other tools
//
// 3. Native performance:
//    - Direct access to audio hardware
//    - Low-latency recording
//    - Efficient memory usage
//
// 4. Features implemented:
//    - 16-bit PCM recording at 16kHz
//    - Automatic device selection
//    - Silence detection and analysis
//    - WAV format conversion
//    - Comprehensive error handling
//
// 5. Benefits over SoX-based solution:
//    - No installation requirements for users
//    - Better error messages
//    - More reliable cross-platform support
//    - Smaller attack surface (no external processes)
