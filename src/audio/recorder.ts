import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { pcm16leToWav } from './pcmToWav';

// Import the recording library
const record = require('node-record-lpcm16');

export class AudioRecorder extends EventEmitter {
  private isRecording = false;
  private audioStream: any = null;
  private audioChunks: Buffer[] = [];
  private tempFilePath: string;
  private recordingOptions: any;
  private recordingStartTime: number = 0;
  private minRecordingDuration: number = 100; // Minimum 100ms recording
  private silenceThreshold: number = 0.01; // Audio level threshold for silence detection
  private minSpeechDuration: number = 500; // Minimum 500ms of actual speech required
  private audioLevels: number[] = []; // Store audio levels for analysis

  constructor() {
    super();
    this.tempFilePath = path.join(os.tmpdir(), `whisper-recording-${Date.now()}.wav`);
    this.recordingOptions = {
      sampleRateHertz: 16000,
      threshold: 0.5,
      verbose: false,
      recordProgram: 'rec', // Use SoX for recording
      silence: '0.1s', // Reduced silence detection for immediate response
      device: null, // Let SoX choose the default device
    };
  }

  async start(): Promise<void> {
    if (this.isRecording) {
      // Recording is already in progress, ignore silently
      return;
    }

    try {
      this.isRecording = true;
      this.audioChunks = [];
      this.recordingStartTime = Date.now();

      console.log('Starting audio recording...');

      // Check if SoX is available before attempting to record
      await this.checkSoxAvailability();

      // Start recording using node-record-lpcm16
      this.audioStream = record.record(this.recordingOptions);

      // Collect audio data
      this.audioStream.stream().on('data', (chunk: Buffer) => {
        if (this.isRecording) { // Only collect if still recording
          this.audioChunks.push(chunk);
        }
      });

      this.audioStream.stream().on('error', (error: Error) => {
        console.error('Recording stream error:', error);
        this.isRecording = false;
        this.emit('error', this.createUserFriendlyError(error));
      });

      this.audioStream.stream().on('end', () => {
        console.log('Recording stream ended');
      });

      console.log('Audio recording started');
      this.emit('recording-started');

    } catch (error) {
      this.isRecording = false;
      const friendlyError = this.createUserFriendlyError(error);
      console.error('Failed to start recording:', friendlyError.message);
      throw friendlyError;
    }
  }

  async stop(outputFormat: 'wav' | 'pcm' = 'wav'): Promise<Buffer | null> {
    if (!this.isRecording) {
      // No recording in progress, ignore silently
      return null;
    }

    try {
      const recordingDuration = Date.now() - this.recordingStartTime;
      console.log(`Stopping audio recording after ${recordingDuration}ms...`);

      this.isRecording = false;

      // Stop the recording stream
      if (this.audioStream) {
        this.audioStream.stop();
        this.audioStream = null;
      }

      // Wait a brief moment for any remaining data to be captured
      await new Promise(resolve => setTimeout(resolve, 50));

      // Combine all audio chunks into a single buffer
      const audioBuffer = this.audioChunks.length > 0 ? Buffer.concat(this.audioChunks) : null;

      // Clear the chunks array
      this.audioChunks = [];

      // Check if recording was too short
      if (recordingDuration < this.minRecordingDuration) {
        console.warn(`Recording too short (${recordingDuration}ms), minimum is ${this.minRecordingDuration}ms`);
        this.emit('recording-too-short', { duration: recordingDuration, minimum: this.minRecordingDuration });
        return null;
      }

      // Check if we actually captured any audio data
      if (!audioBuffer || audioBuffer.length === 0) {
        console.warn('No audio data captured during recording');
        this.emit('no-audio-captured');
        return null;
      }

      // Analyze audio for silence detection
      const audioAnalysis = this.analyzeAudioBuffer(audioBuffer);
      console.log(`Audio analysis: avg=${audioAnalysis.averageLevel.toFixed(4)}, max=${audioAnalysis.maxLevel.toFixed(4)}, speechDuration=${audioAnalysis.speechDuration}ms`);
      
      // Check if the audio is mostly silence
      if (audioAnalysis.averageLevel < this.silenceThreshold) {
        console.warn(`Recording is mostly silence (avg level: ${audioAnalysis.averageLevel.toFixed(4)} < threshold: ${this.silenceThreshold})`);
        this.emit('recording-is-silence', { 
          averageLevel: audioAnalysis.averageLevel, 
          threshold: this.silenceThreshold,
          speechDuration: audioAnalysis.speechDuration
        });
        return null;
      }

      // Check if there's enough actual speech content
      if (audioAnalysis.speechDuration < this.minSpeechDuration) {
        console.warn(`Insufficient speech detected (${audioAnalysis.speechDuration}ms < ${this.minSpeechDuration}ms required)`);
        this.emit('insufficient-speech', { 
          speechDuration: audioAnalysis.speechDuration, 
          required: this.minSpeechDuration 
        });
        return null;
      }

      console.log(`Audio recording stopped. Captured ${audioBuffer.length} bytes in ${recordingDuration}ms`);
      this.emit('recording-stopped', { duration: recordingDuration, bytes: audioBuffer.length });

      // Convert to WAV format if requested (default behavior)
      if (outputFormat === 'wav') {
        const wavBuffer = pcm16leToWav(audioBuffer, this.recordingOptions.sampleRateHertz, 1);
        
        // Debug: Write WAV buffer to temp file if DEBUG_AUDIO is enabled
        if (process.env.DEBUG_AUDIO === '1') {
          const debugFilePath = this.tempFilePath.replace(/\.[^.]*$/, '.wav');
          try {
            fs.writeFileSync(debugFilePath, wavBuffer);
            console.log(`Debug: WAV buffer written to ${debugFilePath} for manual playback`);
          } catch (error) {
            console.warn('Debug: Failed to write WAV buffer to temp file:', error);
          }
        }
        
        return wavBuffer;
      }

      // Return raw PCM data if specified
      return audioBuffer;

    } catch (error) {
      this.isRecording = false;
      const friendlyError = this.createUserFriendlyError(error);
      console.error('Failed to stop recording:', friendlyError.message);
      throw friendlyError;
    }
  }

  isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  async cleanup(): Promise<void> {
    if (this.isRecording) {
      await this.stop();
    }
    
    // Clean up temporary files
    try {
      if (fs.existsSync(this.tempFilePath)) {
        fs.unlinkSync(this.tempFilePath);
      }
    } catch (error) {
      console.warn('Failed to clean up temporary audio file:', error);
    }
  }

  // Method to get supported audio formats
  getSupportedFormats(): string[] {
    return ['wav', 'mp3', 'flac'];
  }

  // Method to set audio quality settings
  setAudioSettings(settings: {
    sampleRate?: number;
    channels?: number;
    bitDepth?: number;
  }): void {
    if (settings.sampleRate) {
      this.recordingOptions.sampleRateHertz = settings.sampleRate;
    }
    console.log('Audio settings updated:', settings);
  }

  /**
   * Check if SoX is available on the system
   */
  private async checkSoxAvailability(): Promise<void> {
    return new Promise((resolve, reject) => {
      const { spawn } = require('child_process');
      const sox = spawn('sox', ['--version']);

      let hasOutput = false;

      sox.stdout.on('data', () => {
        hasOutput = true;
      });

      sox.stderr.on('data', () => {
        hasOutput = true;
      });

      sox.on('close', (code: number) => {
        if (code === 0 || hasOutput) {
          resolve();
        } else {
          reject(new Error('SoX is not installed or not working properly. Please install SoX: sudo apt-get install sox (Linux) or brew install sox (macOS)'));
        }
      });

      sox.on('error', () => {
        reject(new Error('SoX is not installed. Please install SoX: sudo apt-get install sox (Linux) or brew install sox (macOS)'));
      });

      // Timeout after 5 seconds
      setTimeout(() => {
        sox.kill();
        reject(new Error('SoX availability check timed out'));
      }, 5000);
    });
  }

  /**
   * Create user-friendly error messages
   */
  private createUserFriendlyError(error: any): Error {
    const errorMessage = error instanceof Error ? error.message : String(error);

    if (errorMessage.includes('ENOENT') || errorMessage.includes('sox') || errorMessage.includes('rec')) {
      return new Error('Audio recording failed: SoX is not installed. Please install SoX:\n' +
        '• Linux: sudo apt-get install sox\n' +
        '• macOS: brew install sox\n' +
        '• Windows: Download from http://sox.sourceforge.net/');
    }

    if (errorMessage.includes('Device or resource busy')) {
      return new Error('Audio recording failed: Microphone is being used by another application. Please close other audio applications and try again.');
    }

    if (errorMessage.includes('Permission denied')) {
      return new Error('Audio recording failed: Permission denied. Please grant microphone permissions to this application.');
    }

    if (errorMessage.includes('No such device')) {
      return new Error('Audio recording failed: No microphone found. Please connect a microphone and try again.');
    }

    return new Error(`Audio recording failed: ${errorMessage}`);
  }

  /**
   * Stop recording and return audio data in WAV format
   * @deprecated Use stop('wav') instead - this method is kept for backward compatibility
   */
  async stopAndGetWAV(): Promise<Buffer | null> {
    return this.stop('wav');
  }

  /**
   * Force stop recording (for cleanup)
   */
  forceStop(): void {
    if (this.isRecording) {
      this.isRecording = false;
      if (this.audioStream) {
        try {
          this.audioStream.stop();
        } catch (error) {
          console.warn('Error force stopping audio stream:', error);
        }
        this.audioStream = null;
      }
      this.audioChunks = [];
      console.log('Audio recording force stopped');
    }
  }

  /**
   * Analyze audio buffer for silence detection
   */
  private analyzeAudioBuffer(audioBuffer: Buffer): {
    averageLevel: number;
    maxLevel: number;
    speechDuration: number;
  } {
    let sum = 0;
    let maxLevel = 0;
    const samples: number[] = [];
    for (let i = 0; i < audioBuffer.length; i += 2) {
      const sample = audioBuffer.readInt16LE(i);
      const norm = Math.abs(sample) / 32768;
      samples.push(norm);
      sum += norm;
      maxLevel = Math.max(maxLevel, norm);
    }

    if (samples.length === 0) {
      return { averageLevel: 0, maxLevel: 0, speechDuration: 0 };
    }

    const averageLevel = sum / samples.length;

    // Calculate speech duration (time above silence threshold)
    const sampleRate = this.recordingOptions.sampleRateHertz;
    const samplesPerMs = sampleRate / 1000;
    let speechSamples = 0;

    // Use a sliding window to detect speech segments
    const windowSize = Math.floor(samplesPerMs * 100); // 100ms window
    for (let i = 0; i < samples.length - windowSize; i += windowSize) {
      const windowSamples = samples.slice(i, i + windowSize);
      const windowAverage = windowSamples.reduce((sum, sample) => sum + sample, 0) / windowSamples.length;
      
      if (windowAverage > this.silenceThreshold) {
        speechSamples += windowSize;
      }
    }

    const speechDuration = speechSamples / samplesPerMs;

    return {
      averageLevel,
      maxLevel,
      speechDuration
    };
  }

  /**
   * Configure silence detection parameters
   */
  setSilenceDetectionSettings(settings: {
    silenceThreshold?: number;
    minSpeechDuration?: number;
  }): void {
    if (settings.silenceThreshold !== undefined) {
      this.silenceThreshold = settings.silenceThreshold;
    }
    if (settings.minSpeechDuration !== undefined) {
      this.minSpeechDuration = settings.minSpeechDuration;
    }
    console.log('Silence detection settings updated:', {
      silenceThreshold: this.silenceThreshold,
      minSpeechDuration: this.minSpeechDuration
    });
  }
}

// Audio recording implementation notes:
// 
// For a production implementation, you would need to:
// 
// 1. Use a cross-platform audio recording library like:
//    - node-record-lpcm16 (Node.js)
//    - Web Audio API (browser/renderer)
//    - platform-specific solutions (ALSA on Linux, CoreAudio on macOS, WASAPI on Windows)
//
// 2. Handle permissions properly:
//    - Request microphone permissions
//    - Handle permission denials gracefully
//
// 3. Implement proper audio format handling:
//    - Convert to formats supported by Whisper API (WAV, MP3, FLAC, etc.)
//    - Handle different sample rates and bit depths
//
// 4. Add error handling for:
//    - No microphone available
//    - Microphone in use by another application
//    - Audio driver issues
//
// 5. Implement audio level monitoring:
//    - Show visual feedback of recording levels
//    - Detect silence and auto-stop recording
//
// 6. Add audio preprocessing:
//    - Noise reduction
//    - Automatic gain control
//    - Echo cancellation (if needed)
