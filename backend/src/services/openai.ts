import axios, { AxiosResponse } from 'axios';
import FormData from 'form-data';
import { config } from '../config';
import { logger } from '../utils/logger';
import { 
  TranscriptionOptions, 
  ChatCompletionRequest, 
  ChatCompletionResponse,
  TranscriptionResponse,
  ApiError 
} from '../types';

export class OpenAIService {
  private apiKey: string;
  private whisperUrl: string;
  private chatUrl: string;
  private modelsUrl: string;
  private timeout: number;

  constructor() {
    this.apiKey = config.openai.apiKey;
    this.whisperUrl = config.openai.whisperUrl;
    this.chatUrl = config.openai.chatUrl;
    this.modelsUrl = config.openai.modelsUrl;
    this.timeout = config.openai.timeout;
  }

  // Test API key validity
  async testApiKey(): Promise<boolean> {
    try {
      const response = await axios.get(this.modelsUrl, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        timeout: 10000,
      });
      
      return response.status === 200;
    } catch (error) {
      logger.error('OpenAI API key test failed:', error);
      return false;
    }
  }

  // Transcribe audio using Whisper API
  async transcribe(
    audioBuffer: Buffer,
    options: TranscriptionOptions = {}
  ): Promise<string> {
    try {
      logger.info(`Transcribing ${audioBuffer.length} bytes of audio data`);

      // Validate audio buffer
      if (!this.isValidWAVBuffer(audioBuffer)) {
        throw new Error('Invalid audio format: Expected WAV format with proper header');
      }

      // Create form data for the API request
      const formData = new FormData();
      
      // Add the audio file
      formData.append('file', audioBuffer, {
        filename: 'speech.wav',
        contentType: 'audio/wav',
      });
      
      // Add model
      formData.append('model', 'whisper-1');
      
      // Add optional parameters
      if (options.language) {
        formData.append('language', options.language);
      }
      
      if (options.prompt) {
        formData.append('prompt', options.prompt);
      }
      
      if (options.temperature !== undefined) {
        formData.append('temperature', options.temperature.toString());
      }
      
      formData.append('response_format', options.response_format || 'text');

      const response: AxiosResponse = await axios.post(this.whisperUrl, formData, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          ...formData.getHeaders(),
        },
        timeout: this.timeout,
      });

      if (response.data) {
        // Handle different response formats
        if (options.response_format === 'json' || options.response_format === 'verbose_json') {
          return (response.data.text || '').trim();
        } else {
          return (response.data || '').trim();
        }
      }

      throw new Error('No transcription data received');
    } catch (error: any) {
      logger.error('Transcription failed:', error);
      throw this.createAPIError(error, 'Transcription');
    }
  }

  // Generate chat completion using GPT API
  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    try {
      logger.info('Making chat completion request', {
        model: request.model,
        messageCount: request.messages.length,
        maxTokens: request.max_tokens,
      });

      const response: AxiosResponse<ChatCompletionResponse> = await axios.post(
        this.chatUrl,
        request,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: this.timeout,
        }
      );

      if (!response.data) {
        throw new Error('No response data received');
      }

      logger.info('Chat completion successful', {
        id: response.data.id,
        model: response.data.model,
        usage: response.data.usage,
      });

      return response.data;
    } catch (error: any) {
      logger.error('Chat completion failed:', error);
      throw this.createAPIError(error, 'Chat completion');
    }
  }

  // Validate WAV buffer format
  private isValidWAVBuffer(buffer: Buffer): boolean {
    if (buffer.length < 44) {
      logger.warn('Audio buffer too small for WAV format (< 44 bytes)');
      return false;
    }

    // Check for RIFF header
    const riffHeader = buffer.slice(0, 4).toString('ascii');
    if (riffHeader !== 'RIFF') {
      logger.warn(`Invalid WAV header: Expected 'RIFF', got '${riffHeader}'`);
      return false;
    }

    // Check for WAVE format
    const waveFormat = buffer.slice(8, 12).toString('ascii');
    if (waveFormat !== 'WAVE') {
      logger.warn(`Invalid WAV format: Expected 'WAVE', got '${waveFormat}'`);
      return false;
    }

    logger.debug('Valid WAV format detected');
    return true;
  }

  // Create standardized API error
  private createAPIError(error: any, context: string): ApiError {
    const apiError = new Error() as ApiError;
    
    if (error.response) {
      // OpenAI API error
      const status = error.response.status;
      const data = error.response.data;
      
      apiError.message = `${context} failed: ${data.error?.message || 'Unknown API error'}`;
      apiError.statusCode = status;
      apiError.code = data.error?.code || 'api_error';
      
      logger.error('OpenAI API Error:', {
        status,
        error: data.error,
        context,
      });
    } else if (error.request) {
      // Network error
      apiError.message = `${context} failed: Unable to reach OpenAI API`;
      apiError.statusCode = 503;
      apiError.code = 'network_error';
    } else {
      // Other error
      apiError.message = `${context} failed: ${error.message || String(error)}`;
      apiError.statusCode = 500;
      apiError.code = 'internal_error';
    }
    
    return apiError;
  }

  // Get estimated token count for text (rough approximation)
  estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  // Get estimated audio duration from buffer size
  estimateAudioDuration(bufferSize: number, sampleRate = 16000, channels = 1, bitDepth = 16): number {
    // Calculate duration in seconds
    const bytesPerSample = bitDepth / 8;
    const bytesPerSecond = sampleRate * channels * bytesPerSample;
    const headerSize = 44; // WAV header size
    const audioDataSize = bufferSize - headerSize;
    
    return Math.max(0, audioDataSize / bytesPerSecond);
  }
}

export const openaiService = new OpenAIService();

// Helper function to generate AI prompts (matching client-side logic)
export function generateAIPrompts(
  mode: string,
  prompt: string,
  text: string,
  selectedText?: string,
  clipboardContent?: string
): { systemPrompt: string; userPrompt: string } {
  const contextContent = selectedText || clipboardContent || text;

  const prompts = {
    write: {
      system: 'You are a helpful writing assistant. Generate clear, well-written text based on the user\'s request. If the user mentions "clipboard" or "selected text", use the provided content as context.',
      user: shouldUseContext(mode, prompt)
        ? `Write: ${prompt}\n\n${getContextLabel(selectedText)} for context: "${contextContent}"`
        : `Write: ${prompt}`
    },
    answer: {
      system: 'You are a knowledgeable assistant. Provide accurate, helpful answers to questions. Be concise but thorough.',
      user: `Answer the question: ${prompt}`
    },
    rewrite: {
      system: 'You are a text editing assistant. Rewrite the provided text according to the user\'s instructions. Maintain the original meaning unless specifically asked to change it.',
      user: `Rewrite the following text according to these instructions: "${prompt}"\n\nText to rewrite: "${contextContent}"`
    },
    reply: {
      system: 'You are a communication assistant. Help write appropriate replies to messages or emails. Match the tone and formality of the original message.',
      user: `Write a reply to the following message/email: "${selectedText || clipboardContent || text}"\n\nReply instructions: ${prompt}`
    },
    command: {
      system: 'You are a command execution assistant. Interpret user commands and provide appropriate responses or actions.',
      user: `Execute command: ${prompt}`
    }
  };

  const modePrompts = prompts[mode as keyof typeof prompts];
  if (!modePrompts) {
    throw new Error(`Unknown AI mode: ${mode}`);
  }

  return { systemPrompt: modePrompts.system, userPrompt: modePrompts.user };
}

function shouldUseContext(mode: string, prompt: string): boolean {
  const lowerPrompt = prompt.toLowerCase();
  return mode === 'write' && (lowerPrompt.includes('clipboard') || lowerPrompt.includes('selected'));
}

function getContextLabel(selectedText?: string): string {
  return selectedText ? 'selected text' : 'clipboard content';
}
