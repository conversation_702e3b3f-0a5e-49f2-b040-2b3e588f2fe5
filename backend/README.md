# Vibe Typer Backend

Backend service for Vibe Typer that provides OpenAI API proxy with Supabase authentication.

## Features

- **Authentication**: Supabase-based user authentication with JWT tokens
- **OpenAI Proxy**: Secure proxy for Whisper transcription and GPT chat completion APIs
- **Usage Tracking**: Track API usage for future subscription implementation
- **Rate Limiting**: Protect against abuse with configurable rate limits
- **Logging**: Comprehensive logging with Winston
- **Security**: Helmet.js security headers and CORS configuration

## Setup

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account and project
- OpenAI API key

### Installation

1. **Clone and install dependencies:**
   ```bash
   cd backend
   npm install
   ```

2. **Environment Configuration:**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   # Server Configuration
   PORT=3001
   NODE_ENV=development
   
   # OpenAI Configuration
   OPENAI_API_KEY=sk-your-openai-api-key-here
   
   # Supabase Configuration
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
   ```

3. **Database Setup:**
   
   In your Supabase dashboard:
   - Go to SQL Editor
   - Run the contents of `supabase-schema.sql`
   - This creates the necessary tables and policies

4. **Start the server:**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm run build
   npm start
   ```

## API Endpoints

### Authentication

- `POST /api/auth/signup` - Create new user account
- `POST /api/auth/signin` - Sign in user
- `POST /api/auth/signout` - Sign out user
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/reset-password` - Reset password

### OpenAI Proxy

All endpoints require authentication via `Authorization: Bearer <token>` header.

- `GET /api/openai/test` - Test OpenAI API connectivity
- `POST /api/openai/transcribe` - Transcribe audio (Whisper API proxy)
- `POST /api/openai/chat/completions` - Chat completion (GPT API proxy)
- `POST /api/openai/process-ai` - AI processing with predefined prompts
- `GET /api/openai/usage` - Get user usage statistics

### Health

- `GET /api/health` - Basic health check
- `GET /api/health/detailed` - Detailed health check with service status

## Request/Response Formats

### Transcription

**Request:**
```bash
curl -X POST http://localhost:3001/api/openai/transcribe \
  -H "Authorization: Bearer <token>" \
  -F "file=@audio.wav" \
  -F "language=en" \
  -F "temperature=0.0"
```

**Response:**
```
Transcribed text content
```

### Chat Completion

**Request:**
```bash
curl -X POST http://localhost:3001/api/openai/chat/completions \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o-mini",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ],
    "max_tokens": 100
  }'
```

**Response:**
```json
{
  "id": "chatcmpl-...",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4o-mini",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": "Hello! How can I help you today?"
    },
    "finish_reason": "stop"
  }],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

### AI Processing

**Request:**
```bash
curl -X POST http://localhost:3001/api/openai/process-ai \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello world",
    "options": {
      "mode": "write",
      "prompt": "Make this more formal"
    }
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "response": "Greetings, world.",
    "usage": {
      "prompt_tokens": 15,
      "completion_tokens": 8,
      "total_tokens": 23
    }
  }
}
```

## Development

### Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode

### Project Structure

```
backend/
├── src/
│   ├── config/          # Configuration management
│   ├── middleware/      # Express middleware
│   ├── routes/          # API route handlers
│   ├── services/        # Business logic services
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility functions
│   └── server.ts        # Main server file
├── logs/                # Log files (created automatically)
├── dist/                # Compiled JavaScript (created by build)
└── package.json
```

## Deployment

### Environment Variables

Ensure all required environment variables are set in production:

- `NODE_ENV=production`
- `PORT` (default: 3001)
- `OPENAI_API_KEY`
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `JWT_SECRET`

### Security Considerations

- Use strong JWT secrets in production
- Configure CORS origins for your domain
- Set up proper rate limiting
- Monitor logs for suspicious activity
- Keep dependencies updated

### Monitoring

The service includes comprehensive logging. In production:

- Monitor log files in the `logs/` directory
- Set up log aggregation (e.g., ELK stack)
- Monitor the `/api/health/detailed` endpoint
- Track usage patterns via the usage_logs table

## License

MIT
